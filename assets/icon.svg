<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#764ba2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f093fb;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="grad2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4ecdc4;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="256" cy="256" r="240" fill="url(#grad1)" stroke="white" stroke-width="8"/>
  
  <!-- 音符符号 -->
  <g transform="translate(256,256)">
    <!-- 主音符 -->
    <ellipse cx="-60" cy="80" rx="25" ry="18" fill="white" transform="rotate(-20)"/>
    <rect x="-38" y="20" width="8" height="120" fill="white" transform="rotate(-20)"/>
    
    <!-- 第二个音符 -->
    <ellipse cx="20" cy="60" rx="25" ry="18" fill="white" transform="rotate(-20)"/>
    <rect x="42" y="0" width="8" height="100" fill="white" transform="rotate(-20)"/>
    
    <!-- 连接线 -->
    <path d="M -30,-40 Q 0,-60 50,-20" stroke="white" stroke-width="8" fill="none"/>
    
    <!-- 装饰性音符 -->
    <circle cx="-80" cy="-60" r="12" fill="url(#grad2)"/>
    <circle cx="80" cy="-40" r="8" fill="url(#grad2)"/>
    <circle cx="60" cy="100" r="10" fill="url(#grad2)"/>
  </g>
  
  <!-- 外圈装饰 -->
  <circle cx="256" cy="256" r="240" fill="none" stroke="url(#grad2)" stroke-width="4" opacity="0.6"/>
</svg>

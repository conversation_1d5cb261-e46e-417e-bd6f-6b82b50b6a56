<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>🎵 MelodyBox - 音乐播放器</title>
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline';">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #667eea 100%);
            color: white;
            height: 100vh;
            overflow: hidden;
            user-select: none;
        }

        .app-container {
            display: flex;
            height: 100vh;
        }

        /* 侧边栏 */
        .sidebar {
            width: 250px;
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            padding: 20px;
            display: flex;
            flex-direction: column;
        }

        .logo {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            font-size: 1.5em;
            font-weight: bold;
        }

        .logo-icon {
            font-size: 2em;
            margin-right: 10px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .nav-menu {
            list-style: none;
            margin-bottom: 30px;
        }

        .nav-item {
            padding: 12px 16px;
            margin-bottom: 8px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
        }

        .nav-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }

        .nav-item.active {
            background: rgba(255, 255, 255, 0.2);
        }

        .nav-icon {
            margin-right: 12px;
            font-size: 1.2em;
        }

        /* 主内容区 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 20px;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .search-bar {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 12px 20px;
            color: white;
            width: 300px;
            backdrop-filter: blur(10px);
        }

        .search-bar::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .user-profile {
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 8px 16px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }

        .avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            margin-right: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        /* 播放器区域 */
        .player-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .album-art {
            width: 280px;
            height: 280px;
            border-radius: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 4em;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .song-info {
            margin-bottom: 30px;
        }

        .song-title {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .artist-name {
            font-size: 1.2em;
            opacity: 0.8;
        }

        .progress-container {
            width: 100%;
            max-width: 500px;
            margin-bottom: 30px;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff6b6b, #4ecdc4);
            width: 35%;
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        .time-info {
            display: flex;
            justify-content: space-between;
            font-size: 0.9em;
            opacity: 0.7;
        }

        .controls {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .control-btn {
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2em;
            backdrop-filter: blur(10px);
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
        }

        .play-btn {
            width: 70px;
            height: 70px;
            font-size: 1.8em;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
        }

        .play-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 10px 20px rgba(255, 107, 107, 0.3);
        }

        /* 底部播放栏 */
        .bottom-player {
            background: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .mini-controls {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .mini-btn {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            font-size: 1.2em;
            padding: 8px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .mini-btn:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .volume-control {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .volume-slider {
            width: 100px;
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
            outline: none;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="logo">
                <span class="logo-icon">🎵</span>
                MelodyBox
            </div>

            <ul class="nav-menu">
                <li class="nav-item active">
                    <span class="nav-icon">🏠</span>
                    首页
                </li>
                <li class="nav-item">
                    <span class="nav-icon">🔍</span>
                    搜索
                </li>
                <li class="nav-item">
                    <span class="nav-icon">📚</span>
                    我的音乐库
                </li>
                <li class="nav-item">
                    <span class="nav-icon">❤️</span>
                    我喜欢的
                </li>
                <li class="nav-item">
                    <span class="nav-icon">📻</span>
                    电台
                </li>
                <li class="nav-item">
                    <span class="nav-icon">🎧</span>
                    播放列表
                </li>
            </ul>

            <div style="margin-top: auto;">
                <div class="nav-item">
                    <span class="nav-icon">⚙️</span>
                    设置
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <div class="header">
                <input type="text" class="search-bar" placeholder="搜索音乐、艺术家、专辑...">
                <div class="user-profile">
                    <div class="avatar">U</div>
                    <span>用户</span>
                </div>
            </div>

            <!-- 播放器区域 -->
            <div class="player-section">
                <div class="album-art">
                    🎶
                </div>

                <div class="song-info">
                    <div class="song-title">夜空中最亮的星</div>
                    <div class="artist-name">逃跑计划</div>
                </div>

                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                    <div class="time-info">
                        <span>1:23</span>
                        <span>4:16</span>
                    </div>
                </div>

                <div class="controls">
                    <button class="control-btn" onclick="previousSong()">⏮️</button>
                    <button class="control-btn play-btn" onclick="togglePlay()">▶️</button>
                    <button class="control-btn" onclick="nextSong()">⏭️</button>
                    <button class="control-btn" onclick="toggleShuffle()">🔀</button>
                    <button class="control-btn" onclick="toggleRepeat()">🔁</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部播放栏 -->
    <div class="bottom-player">
        <div class="mini-controls">
            <button class="mini-btn">⏮️</button>
            <button class="mini-btn">⏸️</button>
            <button class="mini-btn">⏭️</button>
        </div>

        <div class="volume-control">
            <span>🔊</span>
            <input type="range" class="volume-slider" min="0" max="100" value="70">
        </div>
    </div>

    <script src="renderer.js"></script>
</body>
</html>

# Electron 桌面应用

这是一个使用 Electron 构建的简单桌面应用程序。

## 🚀 快速开始

### 安装依赖
```bash
npm install
```

### 运行应用
```bash
# 启动应用
npm start

# 开发模式（带日志）
npm run dev
```

## 📁 项目结构

```
├── main.js          # 主进程文件
├── index.html       # 应用界面
├── renderer.js      # 渲染进程脚本
├── package.json     # 项目配置
└── README.md        # 说明文档
```

## 🛠️ 开发环境

- **Node.js**: 建议使用 LTS 版本
- **Electron**: 最新稳定版
- **npm**: 包管理器

## 📝 功能特性

- ✅ 基础的 Electron 应用框架
- ✅ 现代化的 UI 界面
- ✅ 版本信息显示
- ✅ 开发者工具支持

## 🔧 配置说明

### 镜像源配置
项目已配置使用中国镜像源以提高下载速度：
- npm 镜像：https://registry.npmmirror.com
- Electron 镜像：https://npmmirror.com/mirrors/electron/

### 窗口配置
- 默认窗口大小：1200x800
- 支持 Node.js 集成
- 可自定义窗口属性

## 📚 学习资源

- [Electron 官方文档](https://www.electronjs.org/docs)
- [Electron 中文文档](https://www.electronjs.org/zh/docs/latest/)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

ISC License

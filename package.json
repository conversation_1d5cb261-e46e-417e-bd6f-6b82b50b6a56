{"name": "electron-app", "version": "1.0.0", "description": "一个简单的 Electron 桌面应用", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --enable-logging", "build": "electron-builder", "build:win": "electron-builder --win", "build:mac": "electron-builder --mac", "build:linux": "electron-builder --linux", "dist": "npm run build", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["electron", "desktop", "app"], "author": "", "license": "ISC", "type": "commonjs", "devDependencies": {"electron": "^37.3.1", "electron-builder": "^26.0.12"}, "build": {"appId": "com.melodybox.app", "productName": "MelodyBox", "directories": {"output": "dist"}, "files": ["main.js", "index.html", "renderer.js", "package.json"], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}]}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}]}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}
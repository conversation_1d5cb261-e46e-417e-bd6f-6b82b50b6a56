// 渲染进程脚本

// 显示版本信息
window.addEventListener('DOMContentLoaded', () => {
  const replaceText = (selector, text) => {
    const element = document.getElementById(selector);
    if (element) element.innerText = text;
  };

  for (const dependency of ['chrome', 'node', 'electron']) {
    replaceText(`${dependency}-version`, process.versions[dependency]);
  }
});

// 测试函数
function showAlert() {
  alert('Electron 应用运行正常！🎉');
}

// 添加一些交互功能
document.addEventListener('DOMContentLoaded', () => {
  console.log('Electron 应用已加载完成');
  
  // 可以在这里添加更多的渲染进程逻辑
  // 比如与主进程通信、处理用户界面等
});

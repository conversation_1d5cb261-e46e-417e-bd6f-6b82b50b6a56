// MelodyBox 音乐播放器 - 渲染进程脚本

let isPlaying = false;
let currentTime = 83; // 1:23 in seconds
let totalTime = 256; // 4:16 in seconds
let volume = 70;
let isShuffled = false;
let isRepeating = false;

// 歌曲列表
const songs = [
    { title: "夜空中最亮的星", artist: "逃跑计划", duration: "4:16" },
    { title: "成都", artist: "赵雷", duration: "5:28" },
    { title: "理想", artist: "赵雷", duration: "4:42" },
    { title: "南方姑娘", artist: "赵雷", duration: "4:18" },
    { title: "消愁", artist: "毛不易", duration: "4:12" }
];

let currentSongIndex = 0;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    console.log('🎵 MelodyBox 音乐播放器已启动');

    // 初始化进度条动画
    startProgressAnimation();

    // 添加导航菜单点击事件
    addNavigationEvents();

    // 添加音量控制事件
    addVolumeControl();

    // 添加搜索功能
    addSearchFunctionality();
});

// 播放/暂停切换
function togglePlay() {
    isPlaying = !isPlaying;
    const playBtn = document.querySelector('.play-btn');
    const miniPlayBtn = document.querySelector('.mini-controls .mini-btn:nth-child(2)');

    if (isPlaying) {
        playBtn.innerHTML = '⏸️';
        miniPlayBtn.innerHTML = '⏸️';
        console.log('▶️ 开始播放');
    } else {
        playBtn.innerHTML = '▶️';
        miniPlayBtn.innerHTML = '▶️';
        console.log('⏸️ 暂停播放');
    }
}

// 上一首
function previousSong() {
    currentSongIndex = (currentSongIndex - 1 + songs.length) % songs.length;
    updateCurrentSong();
    console.log('⏮️ 上一首');
}

// 下一首
function nextSong() {
    currentSongIndex = (currentSongIndex + 1) % songs.length;
    updateCurrentSong();
    console.log('⏭️ 下一首');
}

// 随机播放切换
function toggleShuffle() {
    isShuffled = !isShuffled;
    const shuffleBtn = document.querySelector('.controls .control-btn:nth-child(4)');

    if (isShuffled) {
        shuffleBtn.style.background = 'rgba(255, 107, 107, 0.3)';
        console.log('🔀 随机播放已开启');
    } else {
        shuffleBtn.style.background = 'rgba(255, 255, 255, 0.1)';
        console.log('🔀 随机播放已关闭');
    }
}

// 循环播放切换
function toggleRepeat() {
    isRepeating = !isRepeating;
    const repeatBtn = document.querySelector('.controls .control-btn:nth-child(5)');

    if (isRepeating) {
        repeatBtn.style.background = 'rgba(255, 107, 107, 0.3)';
        console.log('🔁 循环播放已开启');
    } else {
        repeatBtn.style.background = 'rgba(255, 255, 255, 0.1)';
        console.log('🔁 循环播放已关闭');
    }
}

// 更新当前歌曲信息
function updateCurrentSong() {
    const song = songs[currentSongIndex];
    document.querySelector('.song-title').textContent = song.title;
    document.querySelector('.artist-name').textContent = song.artist;
    document.querySelector('.time-info span:last-child').textContent = song.duration;

    // 重置进度
    currentTime = 0;
    updateProgress();
}

// 进度条动画
function startProgressAnimation() {
    setInterval(() => {
        if (isPlaying && currentTime < totalTime) {
            currentTime += 1;
            updateProgress();
        }
    }, 1000);
}

// 更新进度显示
function updateProgress() {
    const progressFill = document.querySelector('.progress-fill');
    const currentTimeSpan = document.querySelector('.time-info span:first-child');

    const percentage = (currentTime / totalTime) * 100;
    progressFill.style.width = percentage + '%';

    const minutes = Math.floor(currentTime / 60);
    const seconds = currentTime % 60;
    currentTimeSpan.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
}

// 添加导航事件
function addNavigationEvents() {
    const navItems = document.querySelectorAll('.nav-item');

    navItems.forEach(item => {
        item.addEventListener('click', () => {
            // 移除所有active类
            navItems.forEach(nav => nav.classList.remove('active'));
            // 添加active类到当前项
            item.classList.add('active');

            const text = item.textContent.trim();
            console.log(`📱 切换到: ${text}`);
        });
    });
}

// 音量控制
function addVolumeControl() {
    const volumeSlider = document.querySelector('.volume-slider');

    volumeSlider.addEventListener('input', (e) => {
        volume = e.target.value;
        console.log(`🔊 音量调整为: ${volume}%`);
    });
}

// 搜索功能
function addSearchFunctionality() {
    const searchBar = document.querySelector('.search-bar');

    searchBar.addEventListener('input', (e) => {
        const query = e.target.value;
        if (query.length > 0) {
            console.log(`🔍 搜索: ${query}`);
        }
    });

    searchBar.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            const query = e.target.value;
            console.log(`🎯 执行搜索: ${query}`);
            // 这里可以添加实际的搜索逻辑
        }
    });
}
